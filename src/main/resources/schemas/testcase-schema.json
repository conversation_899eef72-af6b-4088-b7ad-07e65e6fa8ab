{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Test Case Schema List", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "category": {"type": "string", "enum": ["SIMPLE_BOOKING", "MULTIPLE_BOOKINGS", "RELATIVE_TIME_BOOKING", "SEQUENTIAL_BOOKING", "STANDARD_VALUES", "CONTEXTUAL_BOOKING", "DAY_LOOKUP", "COPY_DAYS", "MISSING_INFORMATION", "TOOL_FAILURE_RESPONSE", "UNSUPPORTED_REQUESTS", "MANIPULATIVE_REQUESTS"]}, "description": {"type": "string"}, "mockedDateTime": {"type": "string", "format": "date-time"}, "mockedDefaultValues": {"type": "object", "properties": {"defaultTimeAccount": {"type": "string"}, "defaultBrakeTime": {"type": "string"}, "defaultWorkTime": {"type": "string"}}, "additionalProperties": false}, "expectedResponse": {"type": "object", "oneOf": [{"type": "object", "properties": {"type": {"const": "makeBookings"}, "bookings": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "timeAccount": {"type": "string"}, "description": {"type": "string"}}, "required": ["start", "end", "timeAccount", "description"], "additionalProperties": false}}}, "required": ["type", "bookings"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"const": "getBookings"}, "date": {"type": "string"}}, "required": ["type", "date"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"const": "TextResponse"}, "text": {"type": "string"}}, "required": ["type", "text"], "additionalProperties": false}]}, "variants": {"type": "array", "items": {"type": "array", "items": {"type": "object", "oneOf": [{"type": "object", "properties": {"type": {"const": "UserMessage"}, "text": {"type": "string"}}, "required": ["type", "text"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"const": "AiMessage"}, "text": {"type": ["string", "null"]}, "toolExecutionRequests": {"type": "array", "maxItems": 1, "items": {"type": "object", "properties": {"name": {"type": "string", "enum": ["makeBookings", "getBookings"]}, "arguments": {"type": "string"}}, "required": ["name", "arguments"], "additionalProperties": false}}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"const": "ToolExecutionResultMessage"}, "toolName": {"type": "string", "enum": ["makeBookings", "getBookings"]}, "result": {"type": "string"}}, "required": ["type", "toolName", "result"], "additionalProperties": false}]}}}}, "required": ["id", "category", "description", "mockedDateTime", "mockedDefault<PERSON><PERSON>ues", "variants", "expectedResponse"], "additionalProperties": false}}
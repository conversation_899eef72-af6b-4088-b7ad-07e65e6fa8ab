{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Model Schema List", "type": "array", "items": {"type": "object", "properties": {"modelTag": {"type": "string"}, "modelName": {"type": "string"}, "systemMessageTemplate": {"type": "string"}, "tools": {"type": "array", "items": {"type": "object", "oneOf": [{"type": "object", "properties": {"type": {"const": "makeBookings"}, "toolDescription": {"type": "string"}}, "required": ["type", "toolDescription"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"const": "getBookings"}, "toolDescription": {"type": "string"}}, "required": ["type", "toolDescription"], "additionalProperties": false}]}}}, "required": ["modelTag", "modelName", "systemMessageTemplate"], "additionalProperties": false}}
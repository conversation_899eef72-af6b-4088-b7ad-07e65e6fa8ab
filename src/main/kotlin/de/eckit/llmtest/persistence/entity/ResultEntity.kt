package de.eckit.llmtest.persistence.entity

import jakarta.persistence.*
import java.io.Serializable

@Embeddable
data class ResultId(
    val testCaseId: Int,
    val variantNumber: Int,
    val repetitionNumber: Int,
    val llm: String
) : Serializable

@Suppress("unused", "JpaDataSourceORMInspection")
@Entity
@IdClass(ResultId::class)
@Table(name = "test_results")
class ResultEntity(

    @Id
    val testCaseId: Int,
    @Id
    val variantNumber: Int,
    @Id
    val repetitionNumber: Int,
    @Id
    val llm: String,

    val category: String,
    val response: String,
    val successful: Boolean,
    val error: String,
    val characterCount: Int,
    val responseTime: Long
)
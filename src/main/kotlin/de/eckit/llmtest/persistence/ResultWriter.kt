package de.eckit.llmtest.persistence

import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.model.Category
import de.eckit.llmtest.model.TestResult
import de.eckit.llmtest.parsing.dto.AiMessageDto
import de.eckit.llmtest.parsing.dto.ToolExecutionRequestDto
import de.eckit.llmtest.persistence.entity.ResultEntity
import de.eckit.llmtest.persistence.repository.ResultRepository
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

/**
 * Service for persisting test results to the database.
 *
 * Converts test results into database entities and stores them
 * for later analysis and reporting.
 */
@Service
class ResultWriter(
    private val testResultRepository: ResultRepository,
    private val objectMapper: ObjectMapper
) {

    /**
     * Persists a test result to the database.
     *
     * Converts the test result into a database entity with all
     * relevant metadata and response information.
     *
     * @param testCaseId Identifier of the test case
     * @param variantNumber Number of the test variant
     * @param repetitionNumber Number of the repetition
     * @param llm Name of the language model used
     * @param category Category of the test case
     * @param testResult The actual test result to persist
     */
    @Transactional
    fun addResult(
        testCaseId: Int,
        variantNumber: Int,
        repetitionNumber: Int,
        llm: String,
        category: Category,
        testResult: TestResult
    ) {
        // Convert response to DTO for JSON serialization
        val responseDto = AiMessageDto(
            testResult.response.aiMessage().text(),
            testResult.response.aiMessage().toolExecutionRequests()?.map { toolExecutionRequest ->
                ToolExecutionRequestDto(toolExecutionRequest.name(), toolExecutionRequest.arguments())
            }
        )

        val responseString = objectMapper.writeValueAsString(responseDto)
        // Remove thinking tags for cleaner storage
        val responseStringWithoutThinking = responseString.replace(Regex("<think>.*?</think>"), "")

        val result = ResultEntity(
            testCaseId,
            variantNumber,
            repetitionNumber,
            llm,
            category.toString(),
            responseString,
            testResult is TestResult.Success,
            (testResult as? TestResult.Failure)?.error?.toString() ?: "",
            responseStringWithoutThinking.length,
            testResult.executionTime
        )

        testResultRepository.save(result)
    }
}
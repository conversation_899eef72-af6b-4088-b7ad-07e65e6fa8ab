package de.eckit.llmtest.configuration

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * Configuration for Jackson JSON processing.
 *
 * Configures the ObjectMapper with Kotlin support, Java time handling,
 * and JSON parsing features required for the application.
 */
@Configuration
class JacksonConfiguration {

    /**
     * Creates a configured ObjectMapper for JSON processing.
     *
     * Includes Kotlin support, Java 8 time module, and allows
     * comments in JSON files for better configuration readability.
     *
     * @return Configured ObjectMapper instance
     */
    @Bean
    fun objectMapper(): ObjectMapper {
        return jacksonObjectMapper()
            .registerModule(JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .configure(JsonParser.Feature.ALLOW_COMMENTS, true)
    }
}

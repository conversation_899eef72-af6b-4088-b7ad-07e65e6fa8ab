package de.eckit.llmtest.configuration

import dev.langchain4j.model.chat.ChatModel
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * Configuration for language model services.
 *
 * Provides configured language model instances for use in validation
 * and other AI-powered operations within the application.
 */
@Configuration
class LanguageModelConfiguration {

    /**
     * Creates a Google Gemini chat model for text validation.
     *
     * This model is used by the TextValidator to compare responses
     * against expected criteria using AI-powered evaluation.
     *
     * @return Configured GoogleAiGeminiChatModel instance
     */
    @Bean
    fun embeddingModel(): ChatModel =
        GoogleAiGeminiChatModel.builder()
            .modelName("gemini-2.0-flash")
            .apiKey(System.getenv("API_KEY"))
            .logRequestsAndResponses(true)
            .build()
}
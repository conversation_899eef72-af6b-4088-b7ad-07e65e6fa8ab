package de.eckit.llmtest.configuration

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.sqlite.SQLiteDataSource
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.sql.DataSource

/**
 * Configuration for database connectivity and setup.
 *
 * Creates timestamped SQLite databases for storing test results,
 * ensuring each test run has its own isolated database file.
 */
@Configuration
class DatabaseConfiguration {

    /**
     * Creates a timestamped SQLite database for test results.
     *
     * Each test run gets its own database file with a timestamp
     * to prevent conflicts and enable historical analysis.
     *
     * @param resultsFolderPath Directory path for storing result databases
     * @return Configured SQLite DataSource
     */
    @Bean
    fun dataSource(
        @Value("\${llmtest.path.results}")
        resultsFolderPath: String
    ): DataSource {
        // Ensure results directory exists
        val dbDirectory = File(resultsFolderPath).apply { mkdirs() }

        // Create timestamped database filename
        val timestamp = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(Date())
        val dbName = "Results_$timestamp.db"
        val dbFile = File(dbDirectory, dbName)
        val url = "jdbc:sqlite:${dbFile.absolutePath}"

        // Configure SQLite data source
        val dataSource = SQLiteDataSource()
        dataSource.url = url

        println("Creating new SQLite database: $dbName")
        return dataSource
    }
}
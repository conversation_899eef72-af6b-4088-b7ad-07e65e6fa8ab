package de.eckit.llmtest.logging

import de.eckit.llmtest.model.Model
import de.eckit.llmtest.model.TestCase
import de.eckit.llmtest.model.TestCaseResult
import de.eckit.llmtest.model.TestResult

/**
 * Centralized logging utility for test execution progress and results.
 *
 * Provides formatted console output for different stages of the testing process,
 * including model information, test progress, and result summaries.
 */
object Logger {

    /**
     * Logs the start of evaluation for a specific model.
     */
    fun logModel(model: Model) {
        println("=".repeat(150))
        println("🔍 Evaluating Model: ${model.modelName} (${model.modelTag})")
        println("=".repeat(150))
    }

    /**
     * Logs the start of a test case execution.
     */
    fun logTestCase(index: Int, size: Int, testCase: TestCase) {
        println("📋 Test Case ${testCase.id} (${index + 1}/${size}): ${testCase.description}\n")
    }

    /**
     * Logs the start of a test variant execution.
     */
    fun logVariant(index: Int) {
        println("🧪 Variant ${index + 1}")
    }

    /**
     * Logs the start of a test repetition.
     */
    fun logRepetition(repetition: Int, totalRepetitions: Int) {
        print("  🔁 Repetition ${repetition + 1}/$totalRepetitions:")
    }

    /**
     * Logs a successful test result with timing and token information.
     */
    fun logSuccess(testResult: TestResult.Success) {
        val executionTimeSeconds = testResult.executionTime / 1_000_000_000.0
        val tokenCount = testResult.response.tokenUsage().outputTokenCount()
        println(" ✅ Success (${"%.2f".format(executionTimeSeconds)} s, $tokenCount tokens)")
    }

    /**
     * Logs a failed test result with error details.
     */
    fun logFailure(testResult: TestResult.Failure) {
        println("❌ Failed (${testResult.error})")
        println()
        println(testResult.response.aiMessage().toString())
        println()
    }

    /**
     * Logs a summary of test case results with statistics.
     */
    fun logSummary(result: TestCaseResult) {
        println(
            """

            📊 Summary
                Success Rate:       ${'\u2009'}${result.successfulTests}/${result.totalTest}
                Avg Response Time:  ${"%.2f".format(result.averageResponseTime)} s
                Avg Speed:          ${"%.2f".format(result.averageResponseSpeed)} tokens/s
            """.trimIndent()
        )
        println("-".repeat(150))
    }
}
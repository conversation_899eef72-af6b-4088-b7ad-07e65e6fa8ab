package de.eckit.llmtest

import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.logging.Logger
import de.eckit.llmtest.model.*
import de.eckit.llmtest.parsing.loader.ModelFileLoader
import de.eckit.llmtest.parsing.loader.TestCaseFileLoader
import de.eckit.llmtest.persistence.ResultWriter
import de.eckit.llmtest.validation.ResponseValidator
import dev.langchain4j.agent.tool.ToolSpecification
import dev.langchain4j.data.message.ChatMessage
import dev.langchain4j.data.message.SystemMessage
import dev.langchain4j.model.chat.ChatModel
import dev.langchain4j.model.chat.request.ChatRequest
import dev.langchain4j.model.chat.request.ChatRequestParameters
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel
import dev.langchain4j.model.ollama.OllamaChatModel
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.CommandLineRunner
import org.springframework.stereotype.Component
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * Simplified test case representation for JSON output.
 *
 * @property id Test case identifier
 * @property category Test case category
 * @property description Human-readable description
 * @property variantCount Number of variants in the test case
 */
data class SimpleTestcase(
    val id: Int,
    val category: Category,
    val description: String,
    val variantCount: Int
)

/**
 * Main orchestrator for LLM testing and evaluation.
 *
 * This component coordinates the entire testing process, from loading
 * configurations and test cases to executing tests and persisting results.
 * It supports multiple models and comprehensive test scenarios.
 */
@Component
class TestRunner(
    private val testCaseFileLoader: TestCaseFileLoader,
    private val modelFileLoader: ModelFileLoader,
    private val responseValidator: ResponseValidator,
    private val resultWriter: ResultWriter,
    @Value("\${llmtest.ollama.url}")
    private val ollamaUrl: String,
    @Value("\${llmtest.tests.pause-between-test}")
    private val pauseBetweenTests: Long,
    @Value("\${llmtest.tests.repetitions}")
    private val repetitionsPerVariant: Int,
    private val objectMapper: ObjectMapper
) : CommandLineRunner {

    /**
     * Application entry point for test execution.
     *
     * Orchestrates the complete testing workflow: loads configurations,
     * initializes models, executes all test cases, and reports results.
     *
     * @param args Command-line arguments (not used)
     */
    override fun run(vararg args: String?) {
        val models = modelFileLoader.loadModels()
        val testCases = testCaseFileLoader.loadTestCases()

        // Create simplified test case overview for JSON output
        val simpleTestCases = testCases.map {
            SimpleTestcase(it.id, it.category, it.description, it.variants.size)
        }
        val json = objectMapper.writeValueAsString(simpleTestCases)
        println("Test cases overview: $json")

        // Execute tests for each configured model
        for (model in models) {
            Logger.logModel(model)

            val chatModel = createChatModel(model.modelTag)
            val chatRequestParameters = createChatRequestParameters(model.tools)

            // Warm up the model for consistent timing measurements
            chatModel.chat("Please respond with 'Yes, I am ready! Model is loaded.'")

            // Execute all test cases for this model
            for ((index, testCase) in testCases.withIndex()) {
                Logger.logTestCase(index, testCases.size, testCase)

                val result = runTestCase(testCase, chatModel, model, chatRequestParameters)

                Logger.logSummary(result)
            }
        }

        println("🎉 Testing completed successfully!")
    }

    /**
     * Executes a complete test case with all its variants and repetitions.
     *
     * Runs each variant multiple times according to configuration,
     * collects results, and calculates aggregate statistics.
     *
     * @param testCase The test case to execute
     * @param chatModel The language model instance
     * @param modelInfo The model configuration information
     * @param chatRequestParameters The request parameters for the model
     * @return Aggregated results for the test case
     */
    private fun runTestCase(
        testCase: TestCase,
        chatModel: ChatModel,
        modelInfo: Model,
        chatRequestParameters: ChatRequestParameters
    ): TestCaseResult {
        val systemMessage = createSystemMessage(modelInfo.systemMessageTemplate, testCase)
        var successfulTests = 0
        var totalExecutionTime = 0L
        var totalTokens = 0

        // Execute each variant of the test case
        for ((variantIndex, variant) in testCase.variants.withIndex()) {
            Logger.logVariant(variantIndex)

            // Repeat each variant multiple times for statistical reliability
            repeat(repetitionsPerVariant) { repetitionIndex ->
                Logger.logRepetition(repetitionIndex, repetitionsPerVariant)

                // Brief pause to prevent overwhelming the GPU/model
                Thread.sleep(pauseBetweenTests)

                val testResult = executeTestVariant(
                    variant,
                    systemMessage,
                    chatRequestParameters,
                    chatModel,
                    testCase.expectedResponse
                )

                // Persist result to database
                resultWriter.addResult(
                    testCase.id,
                    variantIndex + 1,
                    repetitionIndex + 1,
                    modelInfo.modelName,
                    testCase.category,
                    testResult
                )

                // Accumulate statistics and log result
                when (testResult) {
                    is TestResult.Success -> {
                        successfulTests++
                        totalExecutionTime += testResult.executionTime
                        totalTokens += testResult.response.tokenUsage().outputTokenCount()
                        Logger.logSuccess(testResult)
                    }
                    is TestResult.Failure -> Logger.logFailure(testResult)
                }
            }
        }

        val totalTests = testCase.variants.size * repetitionsPerVariant
        val averageResponseTime = if (successfulTests > 0) toSeconds(totalExecutionTime) / successfulTests else 0.0
        val averageResponseSpeed = if (totalExecutionTime > 0) totalTokens / toSeconds(totalExecutionTime) else 0.0

        return TestCaseResult(totalTests, successfulTests, averageResponseTime, averageResponseSpeed)
    }

    /**
     * Executes a single test variant and validates the response.
     *
     * Constructs the chat request, measures execution time,
     * and validates the response against expected outcomes.
     *
     * @param chatHistory List of chat messages for the variant
     * @param systemMessage The system message with context
     * @param chatRequestParameters The request parameters
     * @param chatModel The language model instance
     * @param expectedResponse The expected response for validation
     * @return TestResult with execution details and validation outcome
     */
    private fun executeTestVariant(
        chatHistory: List<ChatMessage>,
        systemMessage: SystemMessage,
        chatRequestParameters: ChatRequestParameters,
        chatModel: ChatModel,
        expectedResponse: ExpectedResponse
    ): TestResult {
        // Build chat request with system message first (required by most LLMs)
        val request = ChatRequest
            .builder()
            .messages(listOf(systemMessage) + chatHistory)
            .parameters(chatRequestParameters)
            .build()

        // Execute request and measure timing
        val startTime = System.nanoTime()
        val response = chatModel.chat(request)
        val endTime = System.nanoTime()
        val executionTime = endTime - startTime

        // Validate response and return appropriate result
        return when (val validationResult = responseValidator.validateResponse(response, expectedResponse)) {
            is ValidationResult.Success -> TestResult.Success(executionTime, response)
            is ValidationResult.Failure -> TestResult.Failure(executionTime, response, validationResult.error)
        }
    }

    /**
     * Converts nanoseconds to seconds.
     */
    private fun toSeconds(nanoseconds: Long): Double {
        return nanoseconds / 1_000_000_000.0
    }

    /**
     * Creates a chat model instance based on the model identifier.
     *
     * Supports both Ollama-hosted models and Google Gemini.
     * Uses consistent temperature settings for reproducible results.
     *
     * @param modelTag Identifier of the model to create
     * @return Configured ChatModel instance
     */
    private fun createChatModel(modelTag: String): ChatModel {
        return if (modelTag == "Gemini") {
            GoogleAiGeminiChatModel.builder()
                .apiKey(System.getenv("API_KEY"))
                .modelName("gemini-2.0-flash")
                .temperature(0.1)
                .build()
        } else {
            OllamaChatModel
                .builder()
                .baseUrl(ollamaUrl)
                .modelName(modelTag)
                .temperature(0.1)
                .build()
        }
    }

    /**
     * Creates chat request parameters with tool specifications.
     *
     * Configures the request parameters to include available tools
     * that the language model can use during conversation.
     *
     * @param tools List of tool specifications available to the model
     * @return Configured ChatRequestParameters object
     */
    private fun createChatRequestParameters(tools: List<ToolSpecification>): ChatRequestParameters {
        return ChatRequestParameters
            .builder()
            .toolSpecifications(tools)
            .build()
    }

    /**
     * Creates a system message with contextualized template values.
     *
     * Replaces template placeholders with actual values from the test case,
     * including mocked date/time and default values for realistic testing.
     *
     * @param systemMessageTemplate The system message template string
     * @param testCase The test case containing replacement values
     * @return Configured SystemMessage with resolved placeholders
     */
    private fun createSystemMessage(systemMessageTemplate: String, testCase: TestCase): SystemMessage {
        val dateFormatter = DateTimeFormatter.ofPattern("EEEE dd.MM.yyyy", Locale.GERMAN)
        val dateTimeFormatter = DateTimeFormatter.ofPattern("EEEE dd.MM.yyyy HH:mm", Locale.GERMAN)

        // Generate last week dates for context
        val lastWeekDates = (0..6).map {
            testCase.mockedDateTime.minusDays(it.toLong()).format(dateFormatter)
        }

        return SystemMessage.from(
            systemMessageTemplate
                .replace("{now}", testCase.mockedDateTime.format(dateTimeFormatter))
                .replace("{yesterday}", testCase.mockedDateTime.minusDays(1).format(dateFormatter))
                .replace("{lastWeek}", lastWeekDates.toString())
                .replace(
                    "{defaultWorkTime}",
                    testCase.mockedDefaultValues.getOrDefault("defaultWorkTime", "not specified")
                )
                .replace(
                    "{defaultBrakeTime}",
                    testCase.mockedDefaultValues.getOrDefault("defaultBrakeTime", "not specified")
                )
                .replace(
                    "{defaultTimeAccount}",
                    testCase.mockedDefaultValues.getOrDefault("defaultTimeAccount", "not specified")
                )
        )
    }
}

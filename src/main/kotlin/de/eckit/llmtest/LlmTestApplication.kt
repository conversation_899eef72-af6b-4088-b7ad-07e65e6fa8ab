package de.eckit.llmtest

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

/**
 * Main Spring Boot application class for the LLM testing framework.
 *
 * This application provides automated testing and comparison of different
 * Large Language Models (LLMs) using Ollama for time tracking chatbot scenarios.
 */
@SpringBootApplication
class LlmTestApplication

/**
 * Application entry point.
 *
 * @param args Command line arguments passed to the application
 */
fun main(args: Array<String>) {
    runApplication<LlmTestApplication>(*args)
}

package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.parsing.dto.AiMessageDto
import de.eckit.llmtest.parsing.dto.ChatMessageDto
import de.eckit.llmtest.parsing.dto.ToolExecutionResultMessageDto
import de.eckit.llmtest.parsing.dto.UserMessageDto
import dev.langchain4j.data.message.AiMessage
import dev.langchain4j.data.message.ChatMessage
import dev.langchain4j.data.message.ToolExecutionResultMessage
import dev.langchain4j.data.message.UserMessage
import dev.langchain4j.internal.Utils

object ChatMessageDtoMapper {
    fun map(dto: ChatMessageDto): ChatMessage {
        return when (dto) {
            is UserMessageDto ->
                UserMessage.from(dto.text)

            is AiMessageDto -> {
                val requests = dto.toolExecutionRequests?.map { ToolExecutionRequestDtoMapper.map(it) }
                if (!Utils.isNullOrBlank(dto.text) && !Utils.isNullOrEmpty(requests)) {
                    AiMessage(dto.text, requests)
                } else if (!Utils.isNullOrBlank(dto.text)) {
                    AiMessage(dto.text)
                } else {
                    AiMessage(requests)
                }
            }

            is ToolExecutionResultMessageDto ->
                ToolExecutionResultMessage(null, dto.toolName, dto.result)
        }
    }
}
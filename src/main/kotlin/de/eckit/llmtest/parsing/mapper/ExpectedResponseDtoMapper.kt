package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.model.ExpectedResponse
import de.eckit.llmtest.model.GetBookingsToolExecution
import de.eckit.llmtest.model.MakeBookingsToolExecution
import de.eckit.llmtest.model.TextResponse
import de.eckit.llmtest.parsing.dto.ExpectedResponseDto
import de.eckit.llmtest.parsing.dto.GetBookingsToolExecutionDto
import de.eckit.llmtest.parsing.dto.MakeBookingsToolExecutionDto
import de.eckit.llmtest.parsing.dto.TextResponseDto

object ExpectedResponseDtoMapper {
    fun map(dto: ExpectedResponseDto): ExpectedResponse {
        return when (dto) {
            is MakeBookingsToolExecutionDto -> {
                MakeBookingsToolExecution(dto.bookings.map { BookingDtoMapper.map(it) })
            }

            is GetBookingsToolExecutionDto -> {
                GetBookingsToolExecution(dto.date)
            }

            is TextResponseDto -> {
                TextResponse(dto.text)
            }
        }
    }
}
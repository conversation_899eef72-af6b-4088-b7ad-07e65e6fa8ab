package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.model.TestCase
import de.eckit.llmtest.parsing.dto.TestCaseDto

object TestCaseDtoMapper {
    fun map(dto: TestCaseDto): TestCase {
        return TestCase(
            id = dto.id,
            category = dto.category,
            description = dto.description,
            mockedDateTime = dto.mockedDateTime,
            mockedDefaultValues = dto.mockedDefaultValues,
            variants = dto.variants.map { chatMessageList ->
                chatMessageList.map { ChatMessageDtoMapper.map(it) }
            },
            expectedResponse = ExpectedResponseDtoMapper.map(dto.expectedResponse)
        )
    }
}
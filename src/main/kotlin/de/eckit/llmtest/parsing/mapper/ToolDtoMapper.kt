package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.parsing.dto.GetBookingToolDto
import de.eckit.llmtest.parsing.dto.MakeBookingsToolDto
import de.eckit.llmtest.parsing.dto.ToolDto
import dev.langchain4j.agent.tool.ToolSpecification
import dev.langchain4j.model.chat.request.json.JsonArraySchema
import dev.langchain4j.model.chat.request.json.JsonObjectSchema

object ToolDtoMapper {
    fun map(dto: ToolDto): ToolSpecification {
        return when (dto) {
            is MakeBookingsToolDto -> mapMakeBookingsToolDto(dto.toolDescription)
            is GetBookingToolDto -> mapGetBookingsToolDto(dto.toolDescription)
        }
    }

    private fun mapMakeBookingsToolDto(toolDescription: String): ToolSpecification {
        val bookingObject = JsonObjectSchema.builder()
            .addStringProperty("start")
            .addStringProperty("end")
            .addStringProperty("timeAccount")
            .addStringProperty("description")
            .build()

        val bookingsArray = JsonArraySchema.builder()
            .description( //todo ggf. auch parametriert oder so
                """
                Eine Liste aus Buchungs-Objekten. Mit den Attributen:
                "start" (String) - Startzeitpunkt im Format "yyyy-MM-dd'T'HH:mm"
                "end" (String) - Endzeitpunkt im Format "yyyy-MM-dd'T'HH:mm"
                "timeAccount" (String) - Das Zeitkonto/ das Projekt
                "description" (String) - Kurze Beschreibung der Tätigkeit
                """.trimIndent()
            )
            .items(
                bookingObject
            ).build()

        val toolParameter = JsonObjectSchema.builder()
            .addProperty("bookings", bookingsArray)
            .build()

        return ToolSpecification.builder()
            .name("makeBookings") //todo ggf. parametriert und auch überall
            .description(toolDescription)
            .parameters(toolParameter)
            .build()
    }

    private fun mapGetBookingsToolDto(toolDescription: String): ToolSpecification {
        val toolParameter = JsonObjectSchema.builder()
            .addStringProperty("date", "Das Datum, im \"yyyy-MM-dd\"-Format")
            .build()

        return ToolSpecification.builder()
            .name("getBookings")
            .description(toolDescription)
            .parameters(toolParameter)
            .build()
    }
}
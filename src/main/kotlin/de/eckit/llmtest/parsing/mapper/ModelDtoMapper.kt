package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.model.Model
import de.eckit.llmtest.parsing.dto.ModelDto

object ModelDtoMapper {
    fun map(dto: ModelDto): Model {
        return Model(
            modelName = dto.modelName,
            modelTag = dto.modelTag,
            systemMessageTemplate = dto.systemMessageTemplate,
            tools = dto.tools.map { ToolDtoMapper.map(it) }
        )
    }
}

package de.eckit.llmtest.parsing.mapper

import de.eckit.llmtest.model.Booking
import de.eckit.llmtest.parsing.dto.GetBookingsArgumentDto
import de.eckit.llmtest.parsing.dto.MakeBookingsArgumentDto
import java.time.LocalDate

object ToolArgumentDtoMapper {
    fun mapMakeBookingsArgument(dto: MakeBookingsArgumentDto): List<Booking> {
        return dto.bookings.map { BookingDtoMapper.map(it) }
    }

    fun mapGetBookingsArgument(dto: GetBookingsArgumentDto): LocalDate {
        return dto.date
    }
}
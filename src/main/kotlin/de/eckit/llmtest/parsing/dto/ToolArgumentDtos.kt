package de.eckit.llmtest.parsing.dto

import java.time.LocalDate

/**
 * DTO for makeBookings tool arguments.
 *
 * Represents the expected structure of arguments passed
 * to the booking creation tool.
 *
 * @property bookings List of bookings to be created
 */
data class MakeBookingsArgumentDto(
    val bookings: List<BookingDto>
)

/**
 * DTO for getBookings tool arguments.
 *
 * Represents the expected structure of arguments passed
 * to the booking retrieval tool.
 *
 * @property date Date for which to retrieve bookings
 */
data class GetBookingsArgumentDto(
    val date: LocalDate
)
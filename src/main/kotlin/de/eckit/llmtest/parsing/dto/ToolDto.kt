package de.eckit.llmtest.parsing.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

/**
 * Sealed class hierarchy for tool configuration DTOs.
 *
 * Represents different types of tools that can be configured
 * for language models in the JSON configuration files.
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = MakeBookingsToolDto::class, name = "makeBookings"),
    JsonSubTypes.Type(value = GetBookingToolDto::class, name = "getBookings")
)
sealed class ToolDto

/**
 * DTO for booking creation tool configuration.
 *
 * @property toolDescription Description of the tool's functionality
 */
data class MakeBookingsToolDto(
    val toolDescription: String
) : ToolDto()

/**
 * DTO for booking retrieval tool configuration.
 *
 * @property toolDescription Description of the tool's functionality
 */
data class GetBookingToolDto(
    val toolDescription: String
) : ToolDto()
package de.eckit.llmtest.parsing.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.time.LocalDate

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = MakeBookingsToolExecutionDto::class, name = "makeBookings"),
    JsonSubTypes.Type(value = GetBookingsToolExecutionDto::class, name = "getBookings"),
    JsonSubTypes.Type(value = TextResponseDto::class, name = "TextResponse")
)
sealed class ExpectedResponseDto

data class MakeBookingsToolExecutionDto(
    val bookings: List<BookingDto>
) : ExpectedResponseDto()

data class GetBookingsToolExecutionDto(
    val date: LocalDate
) : ExpectedResponseDto()

data class TextResponseDto(
    val text: String
) : ExpectedResponseDto()



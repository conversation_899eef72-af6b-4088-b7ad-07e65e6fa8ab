package de.eckit.llmtest.parsing.dto

/**
 * Data Transfer Object for model configuration from JSON.
 *
 * Represents the JSON structure for model definitions,
 * which are later mapped to domain Model objects.
 *
 * @property modelTag Technical identifier for the model
 * @property modelName Human-readable name of the model
 * @property systemMessageTemplate Template for system messages
 * @property tools List of available tools for the model
 */
data class ModelDto(
    val modelTag: String,
    val modelName: String,
    val systemMessageTemplate: String,
    val tools: List<ToolDto>
)

package de.eckit.llmtest.parsing.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = UserMessageDto::class, name = "UserMessage"),
    JsonSubTypes.Type(value = AiMessageDto::class, name = "AiMessage"),
    JsonSubTypes.Type(value = ToolExecutionResultMessageDto::class, name = "ToolExecutionResultMessage")
)
sealed class ChatMessageDto

data class UserMessageDto(
    val text: String
) : ChatMessageDto()

data class AiMessageDto(
    val text: String?,
    val toolExecutionRequests: List<ToolExecutionRequestDto>?
) : ChatMessageDto()

data class ToolExecutionResultMessageDto(
    val toolName: String,
    val result: String
) : ChatMessageDto()


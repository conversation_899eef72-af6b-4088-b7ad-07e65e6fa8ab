package de.eckit.llmtest.parsing.dto

import com.fasterxml.jackson.annotation.JsonFormat
import de.eckit.llmtest.model.Category
import java.time.LocalDateTime

data class TestCaseDto(
    val id: Int,
    val category: Category,
    val description: String,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm")
    val mockedDateTime: LocalDateTime,
    val mockedDefaultValues: Map<String, String>,
    val expectedResponse: ExpectedResponseDto,
    val variants: List<List<ChatMessageDto>>,
)

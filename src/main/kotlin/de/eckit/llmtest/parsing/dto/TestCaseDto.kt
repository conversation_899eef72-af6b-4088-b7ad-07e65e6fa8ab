package de.eckit.llmtest.parsing.dto

import com.fasterxml.jackson.annotation.JsonFormat
import de.eckit.llmtest.model.Category
import java.time.LocalDateTime

/**
 * Data Transfer Object for test case configuration from JSON.
 *
 * Represents the complete structure of a test case as defined
 * in JSON configuration files, including all variants and metadata.
 *
 * @property id Unique identifier for the test case
 * @property category Category classification of the test case
 * @property description Human-readable description of the test
 * @property mockedDateTime Simulated current date/time for testing
 * @property mockedDefaultValues Default values for the test context
 * @property expectedResponse Expected response for validation
 * @property variants Different conversation variants for the test
 */
data class TestCaseDto(
    val id: Int,
    val category: Category,
    val description: String,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm")
    val mockedDateTime: LocalDateTime,
    val mockedDefaultValues: Map<String, String>,
    val expectedResponse: ExpectedResponseDto,
    val variants: List<List<ChatMessageDto>>,
)

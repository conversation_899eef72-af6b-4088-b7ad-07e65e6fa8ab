package de.eckit.llmtest.parsing.loader

import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.model.Model
import de.eckit.llmtest.parsing.dto.ModelDto
import de.eckit.llmtest.parsing.mapper.ModelDtoMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.Resource
import org.springframework.stereotype.Service

/**
 * Service for loading and parsing model configurations from JSON files.
 *
 * Reads model definitions from the configured JSON file and converts
 * them into domain model objects for use in testing.
 */
@Service
class ModelFileLoader(
    private val objectMapper: ObjectMapper,
    @Value("\${llmtest.path.models}")
    private val modelsResource: Resource
) {

    /**
     * Loads all model configurations from the JSON file.
     *
     * Parses the models JSON file and converts DTOs to domain objects.
     * Models are returned in reverse order to prioritize newer models.
     *
     * @return List of configured Model objects
     */
    fun loadModels(): List<Model> {
        val inputStream = modelsResource.inputStream
        val dtos = objectMapper.readValue(inputStream, Array<ModelDto>::class.java).toList()
        return dtos.map { ModelDtoMapper.map(it) }.asReversed()
    }
}

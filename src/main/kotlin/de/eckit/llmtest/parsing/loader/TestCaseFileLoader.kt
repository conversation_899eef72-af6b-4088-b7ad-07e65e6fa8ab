package de.eckit.llmtest.parsing.loader

import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.model.TestCase
import de.eckit.llmtest.parsing.dto.TestCaseDto
import de.eckit.llmtest.parsing.mapper.TestCaseDtoMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ResourceLoader
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service

/**
 * Service for loading and parsing test case configurations from JSON files.
 *
 * Scans for test case JSON files in the configured directory and loads
 * all test cases, providing statistics about the loaded test suite.
 */
@Service
class TestCaseFileLoader(
    private val objectMapper: ObjectMapper,
    @Value("\${llmtest.path.testcases}")
    private val testCasesPath: String,
    private val resourceLoader: ResourceLoader
) {

    /**
     * Loads all test cases from JSON files in the configured directory.
     *
     * Scans for files matching the pattern "*testcases.json" and loads
     * all test cases, then provides statistics about the loaded test suite.
     *
     * @return List of all loaded TestCase objects
     */
    fun loadTestCases(): List<TestCase> {
        val resolver = PathMatchingResourcePatternResolver()
        val resources = resolver.getResources("$testCasesPath/*testcases.json")

        // Load and parse all test case DTOs from JSON files
        val testCaseDtos = resources.flatMap { resource ->
            resource.inputStream.use { inputStream ->
                objectMapper.readValue(inputStream, Array<TestCaseDto>::class.java).toList()
            }
        }

        // Generate and display statistics about loaded test cases
        val categoryStats = testCaseDtos.groupBy { it.category }
            .mapValues { (_, testCasesInCategory) ->
                val testCaseCount = testCasesInCategory.size
                val variantCount = testCasesInCategory.sumOf { it.variants.size }
                Pair(testCaseCount, variantCount)
            }

        println("Test case statistics by category: $categoryStats")

        return testCaseDtos.map { TestCaseDtoMapper.map(it) }
    }
}
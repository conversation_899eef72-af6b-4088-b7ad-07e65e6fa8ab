package de.eckit.llmtest.model

import dev.langchain4j.data.message.ChatMessage
import java.time.LocalDateTime

/**
 * Represents a complete test case for LLM evaluation.
 *
 * A test case contains all necessary information to execute and validate
 * a specific test scenario, including multiple conversation variants
 * and expected outcomes.
 *
 * @property id Unique identifier for the test case
 * @property category Category classification of the test case
 * @property description Human-readable description of what the test evaluates
 * @property mockedDateTime Simulated current date/time for the test context
 * @property mockedDefaultValues Default values to be used during test execution
 * @property expectedResponse Expected response that should be generated
 * @property variants Different conversation variants for the same test scenario
 */
data class TestCase(
    val id: Int,
    val category: Category,
    val description: String,
    val mockedDateTime: LocalDateTime,
    val mockedDefaultValues: Map<String, String>,
    val expectedResponse: ExpectedResponse,
    val variants: List<List<ChatMessage>>,
)
package de.eckit.llmtest.model

import java.time.LocalDate

/**
 * Sealed class hierarchy representing different types of expected responses from LLMs.
 *
 * This hierarchy defines the various response types that can be expected
 * from language models during testing, including tool executions and text responses.
 */
sealed class ExpectedResponse

/**
 * Expected response for booking creation tool execution.
 *
 * @property bookings List of bookings that should be created
 */
data class MakeBookingsToolExecution(
    val bookings: List<Booking>
) : ExpectedResponse()

/**
 * Expected response for booking retrieval tool execution.
 *
 * @property date Date for which bookings should be retrieved
 */
data class GetBookingsToolExecution(
    val date: LocalDate
) : ExpectedResponse()

/**
 * Expected text response without tool execution.
 *
 * @property text Expected text content of the response
 */
data class TextResponse(
    val text: String
) : ExpectedResponse()

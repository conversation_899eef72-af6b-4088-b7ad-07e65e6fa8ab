package de.eckit.llmtest.model

import de.eckit.llmtest.validation.Error
import dev.langchain4j.model.chat.response.ChatResponse

/**
 * Sealed class representing the result of response validation.
 */
sealed class ValidationResult {
    /** Indicates successful validation */
    data object Success : ValidationResult()

    /** Indicates validation failure with specific error */
    data class Failure(val error: Error) : ValidationResult()
}

/**
 * Sealed class representing the result of a single test execution.
 */
sealed class TestResult {
    /** Execution time in nanoseconds */
    abstract val executionTime: Long

    /** The actual response from the language model */
    abstract val response: ChatResponse

    /**
     * Successful test execution.
     *
     * @property executionTime Time taken to execute the test in nanoseconds
     * @property response The response from the language model
     */
    data class Success(
        override val executionTime: Long,
        override val response: ChatResponse
    ) : TestResult()

    /**
     * Failed test execution.
     *
     * @property executionTime Time taken to execute the test in nanoseconds
     * @property response The response from the language model
     * @property error The specific error that caused the failure
     */
    data class Failure(
        override val executionTime: Long,
        override val response: ChatResponse,
        val error: Error
    ) : TestResult()
}

/**
 * Aggregated results for a complete test case execution.
 *
 * @property totalTest Total number of tests executed
 * @property successfulTests Number of tests that passed
 * @property averageResponseTime Average response time in seconds
 * @property averageResponseSpeed Average response speed in tokens per second
 */
data class TestCaseResult(
    val totalTest: Int,
    val successfulTests: Int,
    val averageResponseTime: Double,
    val averageResponseSpeed: Double
)
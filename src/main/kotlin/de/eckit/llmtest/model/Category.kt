package de.eckit.llmtest.model

/**
 * Enumeration of test case categories for LLM evaluation.
 *
 * Each category represents a specific type of test scenario
 * to evaluate different aspects of the language model's capabilities
 * in time tracking and booking contexts.
 */
enum class Category {
    /** Simple, straightforward booking requests */
    SIMPLE_BOOKING,

    /** Multiple bookings in a single request */
    MULTIPLE_BOOKINGS,

    /** Abbreviated or shorthand booking expressions */
    SHORTHAND_BOOKING,

    /** Bookings with relative time references (e.g., "tomorrow", "next week") */
    RELATIVE_TIME_BOOKING,

    /** Sequential booking operations */
    SEQUENTIAL_BOOKING,

    /** Tests using default or standard values */
    STANDARD_VALUES,

    /** Context-dependent booking scenarios */
    CONTEXTUAL_BOOKING,

    /** Day lookup and retrieval operations */
    DAY_LOOKUP,

    /** Copying bookings from one day to another */
    COPY_DAYS,

    /** Handling requests with missing information */
    MISSING_INFORMATION,

    /** Responses to tool execution failures */
    TOOL_FAILURE_RESPONSE,

    /** Handling unsupported or invalid requests */
    UNSUPPORTED_REQUESTS,

    /** Resistance to manipulative or misleading requests */
    MANIPULATIVE_REQUESTS,

    /** Handling of invalid or malformed data */
    INVALID_DATA,

    /** Complex scenarios combining multiple booking types */
    COMBINED_COMPLEX_BOOKINGS
}
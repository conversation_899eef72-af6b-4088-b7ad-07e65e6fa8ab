package de.eckit.llmtest.model

import dev.langchain4j.agent.tool.ToolSpecification

/**
 * Represents a language model configuration for testing.
 *
 * Contains all necessary information to configure and execute tests
 * with a specific LLM, including its identification, system prompt,
 * and available tools.
 *
 * @property modelName Human-readable name of the model
 * @property modelTag Technical identifier used by Ollama or the model provider
 * @property systemMessageTemplate Template for the system message/prompt
 * @property tools List of available tool specifications for the model
 */
data class Model(
    val modelName: String,
    val modelTag: String,
    val systemMessageTemplate: String,
    val tools: List<ToolSpecification>
)
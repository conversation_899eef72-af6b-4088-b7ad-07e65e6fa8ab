package de.eckit.llmtest.model

import java.time.LocalDateTime

/**
 * Represents a time booking entry for time tracking.
 *
 * Contains all necessary information for a single time booking,
 * including the time period, account assignment, and description.
 *
 * @property start Start date and time of the booking
 * @property end End date and time of the booking
 * @property timeAccount Account or project identifier for time allocation
 * @property description Descriptive text for the booking activity
 */
data class Booking(
    val start: LocalDateTime,
    val end: LocalDateTime,
    val timeAccount: String,
    val description: String
)

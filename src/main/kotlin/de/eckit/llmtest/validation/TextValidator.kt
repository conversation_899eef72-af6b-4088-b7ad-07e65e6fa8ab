package de.eckit.llmtest.validation

import de.eckit.llmtest.model.TextResponse
import de.eckit.llmtest.model.ValidationResult
import dev.langchain4j.model.chat.ChatModel
import dev.langchain4j.model.chat.response.ChatResponse
import dev.langchain4j.service.AiServices
import dev.langchain4j.service.UserMessage
import dev.langchain4j.service.V
import org.springframework.stereotype.Service

/**
 * Validator for text-based responses from language models.
 *
 * Uses an AI-powered comparison service to evaluate whether the actual
 * text response meets the criteria specified in the expected response.
 */
@Service
class TextValidator(
    languageModel: ChatModel
) {

    private val responseComparator: ResponseComparator =
        AiServices.create(ResponseComparator::class.java, languageModel)

    /**
     * Validates text responses against expected criteria.
     *
     * Ensures the response is a text response (not a tool execution) and
     * uses AI-powered comparison to evaluate content quality.
     *
     * @param response The actual response from the language model
     * @param expectedResponseDescription The expected text response criteria
     * @return ValidationResult indicating success or failure
     */
    fun validate(response: ChatResponse, expectedResponseDescription: TextResponse): ValidationResult {
        // Ensure this is a text response, not a tool execution
        if (response.aiMessage().hasToolExecutionRequests()) {
            return ValidationResult.Failure(Error.NO_TEXT_RESPONSE)
        }

        // Extract text content, removing any thinking tags
        val actualResponse = response.aiMessage().text()?.replace(Regex("<think>.*?</think>"), "") ?: ""

        // Rate limiting to prevent API throttling
        Thread.sleep(4000)

        return if (responseComparator.compare(actualResponse, expectedResponseDescription.text)) {
            ValidationResult.Success
        } else {
            ValidationResult.Failure(Error.TEXT_IS_NOT_RIGHT)
        }
    }

    /**
     * AI service interface for comparing text responses against expected criteria.
     *
     * Uses a language model to evaluate whether a test sentence meets
     * the criteria specified in a candidate sentence.
     */
    fun interface ResponseComparator {
        @UserMessage(
            """
            You will receive 2 sentences. The first one is a statement, claim, or criterion.
            The second sentence is one that you should check against that statement or criterion.
            If the statement/criterion is met, return 'true', otherwise 'false'.

            Statement/Criterion: '{{candidateSentence}}'

            Sentence to check: '{{testSentence}}'
            """
        )
        fun compare(
            @V("testSentence") testSentence: String,
            @V("candidateSentence") candidateSentence: String
        ): Boolean
    }
}
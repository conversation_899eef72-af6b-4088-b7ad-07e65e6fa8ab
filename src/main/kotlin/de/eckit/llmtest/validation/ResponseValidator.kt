package de.eckit.llmtest.validation

import de.eckit.llmtest.model.*
import dev.langchain4j.model.chat.response.ChatResponse
import org.springframework.stereotype.Component

/**
 * Central coordinator for validating LLM responses against expected outcomes.
 *
 * This component delegates validation to specialized validators based on the
 * type of expected response, providing a unified interface for all validation
 * operations while maintaining separation of concerns.
 */
@Component
class ResponseValidator(
    private val makeBookingsValidator: MakeBookingsValidator,
    private val getBookingsValidator: GetBookingsValidator,
    private val textValidator: TextValidator
) {

    /**
     * Validates an LLM response against the expected response type.
     *
     * Routes the validation to the appropriate specialized validator
     * based on the expected response type.
     *
     * @param response The actual response from the language model
     * @param expectedResponse The expected response for comparison
     * @return ValidationResult indicating success or failure with error details
     */
    fun validateResponse(response: ChatResponse, expectedResponse: ExpectedResponse): ValidationResult {
        return when (expectedResponse) {
            is MakeBookingsToolExecution -> makeBookingsValidator.validate(response, expectedResponse)
            is GetBookingsToolExecution -> getBookingsValidator.validate(response, expectedResponse)
            is TextResponse -> textValidator.validate(response, expectedResponse)
        }
    }
}
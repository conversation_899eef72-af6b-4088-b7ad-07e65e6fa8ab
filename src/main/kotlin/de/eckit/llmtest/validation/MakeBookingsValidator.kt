package de.eckit.llmtest.validation

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.model.Booking
import de.eckit.llmtest.model.MakeBookingsToolExecution
import de.eckit.llmtest.model.ValidationResult
import dev.langchain4j.model.chat.response.ChatResponse
import org.springframework.stereotype.Service

@Service
class MakeBookingsValidator(private val objectMapper: ObjectMapper) {

    /**
     * Validates MakeBookings requests.
     *
     * @param response The chatbot response to validate.
     * @param expectedResponse The expected MakeBookings response.
     * @return ValidationResult indicating success or failure and an optional error message.
     */
    fun validate(
        response: ChatResponse,
        expectedResponse: MakeBookingsToolExecution
    ): ValidationResult {
        val toolExecutionRequests = response.aiMessage().toolExecutionRequests()?.filter { it.name() == "makeBookings" }
            ?: return ValidationResult.Failure(Error.NO_TOOL_CALL_MAKEBOOKINGS)

        val returnedBookings = toolExecutionRequests.flatMap {
            parseMakeBookingsArgument(it.arguments()).getOrElse {
                return ValidationResult.Failure(Error.CANT_PARSE_BOOKING)
            }
        }

        return validateBookings(returnedBookings, expectedResponse.bookings)
    }

    private fun parseMakeBookingsArgument(jsonArguments: String): Result<List<Booking>> {
        return runCatching {
            val rootNode = objectMapper.readTree(jsonArguments)
            val bookingsNode = rootNode["bookings"]
            // Some models (e.g., llama) might return the "bookings" argument as a string instead of a list
            if (bookingsNode.isTextual) {
                objectMapper.readValue(bookingsNode.textValue(), object : TypeReference<List<Booking>>() {})
            } else {
                objectMapper.readValue(bookingsNode.traverse(), object : TypeReference<List<Booking>>() {})
            }
        }
    }

    /**
     * Validates the actual bookings against the expected bookings.
     *
     * @param actualBookings The actual list of bookings returned.
     * @param expectedBookings The list of expected bookings.
     * @return ValidationResult indicating success or failure with an error message.
     */
    private fun validateBookings(
        actualBookings: List<Booking>,
        expectedBookings: List<Booking>
    ): ValidationResult {
        return when {
            actualBookings.size < expectedBookings.size ->
                ValidationResult.Failure(Error.TOO_FEW_BOOKINGS)

            actualBookings.size > expectedBookings.size ->
                ValidationResult.Failure(Error.TOO_MANY_BOOKINGS)

            expectedBookings.size == 1 ->
                validateSingleBooking(actualBookings.first(), expectedBookings.first())

            else ->
                validateMultipleBookings(actualBookings, expectedBookings)
        }
    }

    /**
     * Validates a single actual booking against a single expected booking.
     *
     * @param actual The actual booking returned.
     * @param expected The expected booking.
     * @return ValidationResult indicating success or failure with an error message.
     */
    private fun validateSingleBooking(actual: Booking, expected: Booking): ValidationResult {
        if (actual.timeAccount != expected.timeAccount) {
            return ValidationResult.Failure(Error.TIMEACCOUNT_NOT_MATCHING)
        }

        val datesAreEqual =
            actual.start.toLocalDate() == expected.start.toLocalDate() &&
                    actual.end.toLocalDate() == expected.end.toLocalDate()

        val timesAreEqual =
            actual.start.toLocalTime() == expected.start.toLocalTime() &&
                    actual.end.toLocalTime() == expected.end.toLocalTime()

        return when {
            !datesAreEqual && !timesAreEqual ->
                ValidationResult.Failure(Error.DATETIME_NOT_MATCHING)

            !datesAreEqual ->
                ValidationResult.Failure(Error.DATE_NOT_MATCHING)

            !timesAreEqual ->
                ValidationResult.Failure(Error.TIME_NOT_MATCHING)

            else ->
                ValidationResult.Success
        }
    }

    /**
     * Validates multiple actual bookings against multiple expected bookings.
     *
     * Checks whether each expected booking has a matching counterpart in the actual list.
     *
     * @param actualBookings The actual list of bookings returned.
     * @param expectedBookings The list of expected bookings.
     * @return ValidationResult indicating success or failure with an error message.
     */
    private fun validateMultipleBookings(
        actualBookings: List<Booking>,
        expectedBookings: List<Booking>
    ): ValidationResult {
        val allMatched = expectedBookings.all { expected ->
            actualBookings.any { actual -> bookingsMatch(actual, expected) }
        }
        return if (allMatched) {
            ValidationResult.Success
        } else {
            ValidationResult.Failure(Error.NO_MATCHING_BOOKING)
        }
    }

    /**
     * Checks if two Booking objects match in start time, end time, and timeAccount.
     *
     * @param a The first booking.
     * @param b The second booking.
     * @return true if both bookings match; false otherwise.
     */
    private fun bookingsMatch(a: Booking, b: Booking): Boolean {
        return a.start == b.start &&
                a.end == b.end &&
                a.timeAccount == b.timeAccount
    }
}

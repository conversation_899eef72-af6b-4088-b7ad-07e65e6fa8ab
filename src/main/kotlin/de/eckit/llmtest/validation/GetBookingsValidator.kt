package de.eckit.llmtest.validation

import com.fasterxml.jackson.databind.ObjectMapper
import de.eckit.llmtest.model.GetBookingsToolExecution
import de.eckit.llmtest.model.ValidationResult
import de.eckit.llmtest.parsing.dto.GetBookingsArgumentDto
import de.eckit.llmtest.parsing.mapper.ToolArgumentDtoMapper
import dev.langchain4j.model.chat.response.ChatResponse
import org.springframework.stereotype.Service
import java.time.LocalDate

/**
 * Validator for booking retrieval tool execution responses.
 *
 * Validates that the language model correctly calls the getBookings tool
 * with the expected date parameter when requested to retrieve bookings.
 */
@Service
class GetBookingsValidator(private val objectMapper: ObjectMapper) {

    /**
     * Validates getBookings tool execution requests.
     *
     * Ensures the response contains a getBookings tool call with the
     * correct date parameter matching the expected value.
     *
     * @param response The actual response from the language model
     * @param expectedResponse The expected getBookings tool execution
     * @return ValidationResult indicating success or failure with error details
     */
    fun validate(
        response: ChatResponse,
        expectedResponse: GetBookingsToolExecution
    ): ValidationResult {
        // Check if getBookings tool was called
        val toolExecutionRequest = response.aiMessage().toolExecutionRequests()?.find { it.name() == "getBookings" }
            ?: return ValidationResult.Failure(Error.NO_TOOL_CALL_GETBOOKINGS)

        // Parse and validate the date argument
        val returnedDate = parseGetBookingsArgument(toolExecutionRequest.arguments()).getOrElse {
            return ValidationResult.Failure(Error.CANT_PARSE_DATE)
        }

        return if (expectedResponse.date.isEqual(returnedDate)) {
            ValidationResult.Success
        } else {
            ValidationResult.Failure(Error.WRONG_DATE)
        }
    }

    /**
     * Parses the JSON arguments of a getBookings tool call.
     *
     * @param jsonArguments JSON string containing the tool arguments
     * @return Result containing the parsed date or an error
     */
    private fun parseGetBookingsArgument(jsonArguments: String): Result<LocalDate> {
        return kotlin.runCatching {
            val argumentDto = objectMapper.readValue(jsonArguments, GetBookingsArgumentDto::class.java)
            ToolArgumentDtoMapper.mapGetBookingsArgument(argumentDto)
        }
    }
}
package de.eckit.llmtest.validation

/**
 * Enumeration of validation errors that can occur during LLM response validation.
 *
 * These errors represent specific failure modes when validating language model
 * responses against expected outcomes in different test scenarios.
 */
enum class Error {
    /** Expected makeBookings tool call but none was found */
    NO_TOOL_CALL_MAKEBOOKINGS,

    /** Unable to parse booking data from tool arguments */
    CANT_PARSE_BOOKING,

    /** More bookings returned than expected */
    TOO_MANY_BOOKINGS,

    /** Fewer bookings returned than expected */
    TOO_FEW_BOOKINGS,

    /** No matching booking found in the expected set */
    NO_MATCHING_BOOKING,

    /** Both date and time components don't match expected values */
    DATETIME_NOT_MATCHING,

    /** Date component doesn't match expected value */
    DATE_NOT_MATCHING,

    /** Time component doesn't match expected value */
    TIME_NOT_MATCHING,

    /** Time account doesn't match expected value */
    TIMEACCOUNT_NOT_MATCHING,

    /** Expected getBookings tool call but none was found */
    NO_TOOL_CALL_GETBOOKINGS,

    /** Unable to parse date from tool arguments */
    CANT_PARSE_DATE,

    /** Date parameter doesn't match expected value */
    WRONG_DATE,

    /** Expected text response but tool execution was requested */
    NO_TEXT_RESPONSE,

    /** Text content doesn't match expected criteria */
    TEXT_IS_NOT_RIGHT
}
# LLM-Testanwendung für Zeiterfassungs-Chatbot

Diese Anwendung dient zum automatisierten Vergleich unterschiedlicher lokaler Large Language Models (LLMs) mit<PERSON><PERSON> <PERSON>, um deren Eignung für einen spezifischen Chatbot zur Zeiterfassung (z. B. Zeitbuchungen) zu testen. Ziel ist es,
das optimale lokale LLM hinsichtlich Genauigkeit, Tool-Nutzung und Performance zu finden.

Die Anwendung ist mit Spring Boot und Kotlin implementiert und nutzt LangChain4j für die Kommunikation mit den LLMs.

## 🛠️ Voraussetzungen

- Java (mindestens Version 21, über Gradle konfiguriert)
- Kotlin
- Gradle (als Build-Tool)
- Ollama (zum Hosten der lokalen LLMs)
- API-Key (`API_KEY`) für Gemini Embeddings (z. B. über IDE-Umgebungsvariable setzen)

## 📂 Projektstruktur

```
src
├── main
│   ├── resources
│   │   ├── testcases.json
│   │   ├── models.json
│   │   └── schemas
│   │       ├── testcase-schema.json
│   │       └── model-schema.json
│   │   └── results
|   |       └── (SQLite-DBs je Testlauf)
│   └── kotlin
│       └── de.eckit.llmtest
│           ├── TestRunner.kt
│           └── ...
├── application.properties
```

## 🚀 Anwendung starten

1. Modelle via Ollama herunterladen.
2. Gemini API-Key als Umgebungsvariable anlegen.
3. JSON-Dateien konfigurieren (siehe Abschnitt "Konfiguration").
4. Programm starten.

## ⚙️ Konfiguration

Die zentrale Steuerung erfolgt über `application.properties`. Wesentliche Optionen:

- `llmtest.ollama.url`: URL zum lokalen Ollama-Server
- `llmtest.tests.repetitions`: Anzahl der Wiederholungen je Variante
- `llmtest.tests.pause-between-test`: Pause zwischen Testdurchläufen (ms)
- `llmtest.validate.textsimilarity`: Mindestähnlichkeit zur Validierung der Antworten

Die Testfälle und Modelle werden in JSON-Dateien definiert, deren Struktur in den Schemas beschrieben ist:

- **`testcases.json`** ([Schema](src/main/resources/schemas/testcase-schema.json)): Definiert Testfälle, Varianten und
  erwartete Ergebnisse.
- **`models.json`** ([Schema](src/main/resources/schemas/model-schema.json)): Definiert Modelle, Systemnachrichten und
  unterstützte Tools.

## 🗃️ Ergebnisse

Testergebnisse werden automatisch in SQLite-Datenbanken im Ordner `results/` gespeichert. Pro Durchlauf wird eine
separate Datenbank erzeugt.

